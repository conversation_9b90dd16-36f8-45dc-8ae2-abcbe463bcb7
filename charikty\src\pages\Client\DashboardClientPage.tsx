import React from 'react';
import { Typography, Grid, Card, CardContent, Box } from '@mui/material';
import { IconDashboard, IconUser, IconBell, IconTrendingUp, IconGift, IconBuilding } from '@tabler/icons-react';
import PageContainer from '../../components/container/PageContainer';
import DashboardCard from '../../components/shared/DashboardCard';
import { useAuthContext } from '../../contexts/AuthContext';
import NotreService from "../../components/client/NotreService";
import OrdersTable from "../../components/order/OrdersTable";

const DashboardClient = () => {
  const { user } = useAuthContext();

  // Dashboard statistics cards data
  const dashboardStats = [
    {
      title: 'Total Orders',
      value: '24',
      icon: IconDashboard,
      color: 'primary.main',
      bgColor: 'primary.light',
    },
    {
      title: 'Pending Orders',
      value: '5',
      icon: IconTrendingUp,
      color: 'warning.main',
      bgColor: 'warning.light',
    },
    {
      title: 'Completed Orders',
      value: '19',
      icon: IconGift,
      color: 'success.main',
      bgColor: 'success.light',
    },
    {
      title: 'Notifications',
      value: '3',
      icon: IconBell,
      color: 'error.main',
      bgColor: 'error.light',
    },
  ];

  return (
    <PageContainer title="Dashboard Client" description="Client Dashboard">
      <Box>
        <Typography variant="h4" mb={3}>
          Bienvenue, {user?.name || 'Client'}!
        </Typography>

        {/* Statistics Cards */}
        <Grid container spacing={3} mb={4}>
          {dashboardStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <DashboardCard>
                <Box display="flex" alignItems="center">
                  <Box
                    sx={{
                      bgcolor: stat.bgColor,
                      width: 48,
                      height: 48,
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <stat.icon size="24" color={stat.color} />
                  </Box>
                  <Box>
                    <Typography variant="h4" color="textPrimary">
                      {stat.value}
                    </Typography>
                    <Typography variant="subtitle2" color="textSecondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
              </DashboardCard>
            </Grid>
          ))}
        </Grid>

        {/* Services Section */}
        <Grid container spacing={3}>
          <Grid item xs={12} lg={8}>
            <DashboardCard title="Nos Services">
              <NotreService onServiceClick={() => {}} />
            </DashboardCard>
          </Grid>

          <Grid item xs={12} lg={4}>
            <DashboardCard title="Quick Actions">
              <Box display="flex" flexDirection="column" gap={2}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center">
                      <IconUser size="20" />
                      <Typography variant="body2" ml={1}>
                        Update Profile
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center">
                      <IconBuilding size="20" />
                      <Typography variant="body2" ml={1}>
                        Domiciliation Services
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            </DashboardCard>
          </Grid>
        </Grid>

        {/* Orders Table */}
        <Box mt={3}>
          <DashboardCard title="Recent Orders">
            <OrdersTable />
          </DashboardCard>
        </Box>
      </Box>
    </PageContainer>
  );
};

export default DashboardClient;
