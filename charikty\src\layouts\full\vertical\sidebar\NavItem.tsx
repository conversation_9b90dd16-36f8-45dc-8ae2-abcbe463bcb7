import React from 'react';
import { NavLink } from 'react-router-dom';
import { ListItemIcon, ListItem, List, styled, ListItemText, useTheme, ListItemButton } from '@mui/material';
import {
  IconDashboard,
  IconUser,
  IconBell,
  IconTrendingUp,
  IconGift,
  IconBuilding,
  IconUsers,
  IconTruck,
  IconBuildingStore,
  IconChartBar,
  IconSettings,
  IconClipboardList,
  IconCurrencyDollar,
  IconHelp
} from '@tabler/icons-react';

type NavGroup = {
  [x: string]: any;
  id?: string;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: any;
  children?: NavGroup[];
  chip?: string;
  chipColor?: string;
  variant?: string | any;
  external?: boolean;
  level?: number;
  onClick?: (event: React.MouseEvent<HTMLElement>) => void;
};

interface ItemType {
  item: NavGroup;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
  hideMenu?: any;
  level?: number | any;
  pathDirect: string;
}

const NavItem = ({ item, level, pathDirect, onClick }: ItemType) => {
  const theme = useTheme();

  // Map icon names to actual icon components
  const getIcon = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      'dashboard': IconDashboard,
      'person': IconUser,
      'notifications': IconBell,
      'trending_up': IconTrendingUp,
      'local_offer': IconGift,
      'location_city': IconBuilding,
      'people': IconUsers,
      'delivery_dining': IconTruck,
      'business': IconBuildingStore,
      'bar_chart': IconChartBar,
      'settings': IconSettings,
      'assignment': IconClipboardList,
      'attach_money': IconCurrencyDollar,
      'help': IconHelp,
    };

    const IconComponent = iconMap[iconName] || IconDashboard;
    return <IconComponent size="1.3rem" />;
  };

  const itemIcon = item.icon ? getIcon(item.icon) : null;

  const ListItemStyled = styled(ListItem)(() => ({
    padding: 0,
    '.MuiButtonBase-root': {
      whiteSpace: 'nowrap',
      marginBottom: '2px',
      padding: '8px 10px',
      borderRadius: '8px',
      backgroundColor: level > 1 ? 'transparent !important' : 'inherit',
      color: theme.palette.text.secondary,
      paddingLeft: '10px',
      '&:hover': {
        backgroundColor: theme.palette.primary.light,
        color: theme.palette.primary.main,
      },
      '&.Mui-selected': {
        color: 'white',
        backgroundColor: theme.palette.primary.main,
        '&:hover': {
          backgroundColor: theme.palette.primary.main,
          color: 'white',
        },
      },
    },
  }));

  return (
    <List component="div" disablePadding key={item.id}>
      <ListItemStyled>
        <ListItemButton
          component={NavLink}
          to={item.href}
          disabled={item.disabled}
          selected={pathDirect === item.href}
          target={item.external ? '_blank' : ''}
          onClick={onClick}
        >
          <ListItemIcon
            sx={{
              minWidth: '36px',
              p: '3px 0',
              color: 'inherit',
            }}
          >
            {itemIcon}
          </ListItemIcon>
          <ListItemText>
            <>{item.title}</>
          </ListItemText>
        </ListItemButton>
      </ListItemStyled>
    </List>
  );
};

export default NavItem;
