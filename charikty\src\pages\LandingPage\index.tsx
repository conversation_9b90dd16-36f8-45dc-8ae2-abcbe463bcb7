import React from 'react';
import { Typo<PERSON>, Box, Button, Container } from '@mui/material';
import { Link } from 'react-router-dom';

const LandingPage = () => {
  return (
    <Container maxWidth="lg">
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        textAlign="center"
      >
        <Typography variant="h2" component="h1" gutterBottom>
          Charikty Platform
        </Typography>
        
        <Typography variant="h5" component="h2" gutterBottom color="textSecondary">
          Votre solution complète de services
        </Typography>
        
        <Box mt={4}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            component={Link}
            to="/home"
            sx={{ mr: 2 }}
          >
            Accéder à la plateforme
          </Button>
          
          <Button
            variant="outlined"
            color="primary"
            size="large"
            component={Link}
            to="/auth/login"
          >
            Se connecter
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default LandingPage;
