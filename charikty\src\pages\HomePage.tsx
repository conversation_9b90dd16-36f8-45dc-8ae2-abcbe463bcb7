import React from 'react';
import { Typo<PERSON>, Box, Button, Grid } from '@mui/material';
import { Link } from 'react-router-dom';
import PageContainer from '../components/container/PageContainer';
import DashboardCard from '../components/shared/DashboardCard';

const HomePage = () => {
  return (
    <PageContainer title="Accueil" description="Page d'accueil Charikty">
      <Box>
        <Typography variant="h3" mb={3} textAlign="center">
          Bienvenue sur Charikty Platform
        </Typography>

        <Typography variant="h6" mb={4} textAlign="center" color="textSecondary">
          Votre plateforme de services intégrée
        </Typography>

        <Grid container spacing={3} justifyContent="center">
          <Grid item xs={12} md={4}>
            <DashboardCard title="Espace Client">
              <Typography variant="body2" mb={2}>
                Accédez à votre espace client pour gérer vos commandes et services.
              </Typography>
              <Button
                variant="contained"
                color="primary"
                component={Link}
                to="/client/dashboard"
                fullWidth
              >
                Dashboard Client
              </Button>
            </DashboardCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <DashboardCard title="Espace Admin">
              <Typography variant="body2" mb={2}>
                Interface d'administration pour gérer la plateforme.
              </Typography>
              <Button
                variant="contained"
                color="secondary"
                component={Link}
                to="/admin/dashboard"
                fullWidth
              >
                Dashboard Admin
              </Button>
            </DashboardCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <DashboardCard title="Espace Coursier">
              <Typography variant="body2" mb={2}>
                Espace dédié aux coursiers pour gérer les livraisons.
              </Typography>
              <Button
                variant="contained"
                color="success"
                component={Link}
                to="/coursier/dashboard"
                fullWidth
              >
                Dashboard Coursier
              </Button>
            </DashboardCard>
          </Grid>
        </Grid>
      </Box>
    </PageContainer>
  );
};

export default HomePage;